# K8sGPT MCP Access Without Operator

## Option 1: Helm Chart Deployment (Recommended for Production)

### Step 1: Create MCP Values File
```yaml
# values-mcp-production.yaml
deployment:
  mcp:
    enabled: true
    port: "8089"
    http: true
  
  image:
    repository: ghcr.io/k8sgpt-ai/k8sgpt
    tag: "latest"
  
  env:
    model: "gpt-3.5-turbo"
    backend: "openai"
  
  resources:
    limits:
      cpu: "1"
      memory: "512Mi"
    requests:
      cpu: "0.2"
      memory: "156Mi"

# Service configuration for external access
service:
  type: LoadBalancer  # Change to NodePort or ClusterIP as needed
  annotations: {}

# Secret for AI backend
secret:
  secretKey: "your-base64-encoded-openai-key"

# Optional: Enable metrics
serviceMonitor:
  enabled: false
```

### Step 2: Deploy with <PERSON><PERSON>
```bash
# Clone the repository
git clone https://github.com/k8sgpt-ai/k8sgpt.git
cd k8sgpt

# Deploy with MCP enabled
helm install k8sgpt-mcp ./charts/k8sgpt \
  -f values-mcp-production.yaml \
  -n k8sgpt \
  --create-namespace

# Check deployment
kubectl get pods -n k8sgpt
kubectl get svc -n k8sgpt
```

### Step 3: Access MCP Server
```bash
# Option A: Port Forward (Development)
kubectl port-forward -n k8sgpt svc/k8sgpt-mcp 8089:8089

# Option B: Get LoadBalancer IP (Production)
kubectl get svc -n k8sgpt k8sgpt-mcp

# Test MCP endpoint
curl -X POST http://localhost:8089/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {
        "tools": {},
        "resources": {},
        "prompts": {}
      },
      "clientInfo": {
        "name": "test-client",
        "version": "1.0.0"
      }
    }
  }'
```

## Option 2: Standalone Kubernetes Deployment

### Step 1: Create Deployment Manifests
```yaml
# k8sgpt-mcp-standalone.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: k8sgpt-mcp
---
apiVersion: v1
kind: Secret
metadata:
  name: ai-backend-secret
  namespace: k8sgpt-mcp
type: Opaque
data:
  secret-key: "your-base64-encoded-openai-key"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: k8sgpt
  namespace: k8sgpt-mcp
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: k8sgpt-reader
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: k8sgpt-reader-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: k8sgpt-reader
subjects:
- kind: ServiceAccount
  name: k8sgpt
  namespace: k8sgpt-mcp
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: k8sgpt-mcp
  namespace: k8sgpt-mcp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: k8sgpt-mcp
  template:
    metadata:
      labels:
        app: k8sgpt-mcp
    spec:
      serviceAccountName: k8sgpt
      containers:
      - name: k8sgpt
        image: ghcr.io/k8sgpt-ai/k8sgpt:latest
        args: ["serve", "--mcp", "--mcp-http", "--mcp-port", "8089", "-v"]
        ports:
        - containerPort: 8080
          name: grpc
        - containerPort: 8081
          name: metrics
        - containerPort: 8089
          name: mcp
        env:
        - name: K8SGPT_MODEL
          value: "gpt-3.5-turbo"
        - name: K8SGPT_BACKEND
          value: "openai"
        - name: K8SGPT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-backend-secret
              key: secret-key
        - name: XDG_CONFIG_HOME
          value: /k8sgpt-config/
        - name: XDG_CACHE_HOME
          value: /k8sgpt-config/
        volumeMounts:
        - mountPath: /k8sgpt-config
          name: config
        resources:
          limits:
            cpu: "1"
            memory: "512Mi"
          requests:
            cpu: "0.2"
            memory: "156Mi"
      volumes:
      - name: config
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: k8sgpt-mcp
  namespace: k8sgpt-mcp
spec:
  selector:
    app: k8sgpt-mcp
  ports:
  - name: grpc
    port: 8080
    targetPort: 8080
  - name: metrics
    port: 8081
    targetPort: 8081
  - name: mcp
    port: 8089
    targetPort: 8089
  type: ClusterIP  # Change to LoadBalancer or NodePort as needed
```

### Step 2: Deploy
```bash
# Apply the manifests
kubectl apply -f k8sgpt-mcp-standalone.yaml

# Check status
kubectl get all -n k8sgpt-mcp

# Access via port-forward
kubectl port-forward -n k8sgpt-mcp svc/k8sgpt-mcp 8089:8089
```

## Option 3: Local Binary with Cluster Access

### Step 1: Install k8sgpt Binary
```bash
# Install via brew (macOS)
brew install k8sgpt

# Or download binary
curl -LO https://github.com/k8sgpt-ai/k8sgpt/releases/latest/download/k8sgpt_$(uname -s)_$(uname -m).tar.gz
tar -xzf k8sgpt_*.tar.gz
sudo mv k8sgpt /usr/local/bin/
```

### Step 2: Configure Authentication
```bash
# Configure AI backend
k8sgpt auth add openai
# Enter your OpenAI API key when prompted

# Or set environment variable
export K8SGPT_PASSWORD="your-openai-api-key"
export K8SGPT_BACKEND="openai"
export K8SGPT_MODEL="gpt-3.5-turbo"
```

### Step 3: Run MCP Server Locally
```bash
# Start MCP server with HTTP transport
k8sgpt serve --mcp --mcp-http --mcp-port 8089

# Or with STDIO for Claude Desktop
k8sgpt serve --mcp
```

## Option 4: Docker Container with Cluster Access

### Step 1: Create Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  k8sgpt-mcp:
    image: ghcr.io/k8sgpt-ai/k8sgpt:latest
    command: ["serve", "--mcp", "--mcp-http", "--mcp-port", "8089"]
    ports:
      - "8089:8089"
      - "8080:8080"
      - "8081:8081"
    environment:
      - K8SGPT_BACKEND=openai
      - K8SGPT_MODEL=gpt-3.5-turbo
      - K8SGPT_PASSWORD=your-openai-api-key
    volumes:
      - ~/.kube:/root/.kube:ro  # Mount kubeconfig
    network_mode: host  # For cluster access
```

### Step 2: Run
```bash
# Start the container
docker-compose up -d

# Check logs
docker-compose logs -f k8sgpt-mcp

# Test MCP endpoint
curl http://localhost:8089/mcp
```

## 🎯 **MCP Features Available in All Options**

All deployment methods provide access to these MCP tools:

### 1. **Analyze Tool**
```bash
# Example MCP call to analyze cluster
curl -X POST http://localhost:8089/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "analyze",
      "arguments": {
        "namespace": "default",
        "backend": "openai",
        "explain": true,
        "filters": ["Pod", "Service"]
      }
    }
  }'
```

### 2. **Cluster Info Tool**
```bash
# Get cluster information
curl -X POST http://localhost:8089/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "cluster-info",
      "arguments": {}
    }
  }'
```

### 3. **Config Tool**
```bash
# Configure k8sgpt settings
curl -X POST http://localhost:8089/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "config",
      "arguments": {
        "customAnalyzers": [
          {
            "name": "my-analyzer",
            "connection": {
              "url": "http://my-analyzer.example.com",
              "port": 8080
            }
          }
        ]
      }
    }
  }'
```

## 🔧 **Claude Desktop Integration**

For any of the above options, you can integrate with Claude Desktop:

### Option A: HTTP Transport (In-Cluster)
```json
{
  "mcpServers": {
    "k8sgpt": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "http://your-k8sgpt-mcp-endpoint:8089/mcp",
        "-H", "Content-Type: application/json",
        "-d", "@-"
      ]
    }
  }
}
```

### Option B: Local Binary (STDIO)
```json
{
  "mcpServers": {
    "k8sgpt": {
      "command": "k8sgpt",
      "args": ["serve", "--mcp"]
    }
  }
}
```

## 📊 **Comparison of Options**

| Option | Pros | Cons | Best For |
|--------|------|------|----------|
| **Helm Chart** | Production-ready, persistent, full k8s integration | Requires Helm knowledge | Production deployments |
| **Standalone K8s** | Simple, no Helm dependency | Manual manifest management | Simple production setups |
| **Local Binary** | Easy setup, direct control | Requires local installation | Development, testing |
| **Docker Container** | Isolated, portable | Requires Docker, network config | Development, CI/CD |

## 🎯 **Recommended Approach**

**For Production**: Use **Option 1 (Helm Chart)** with LoadBalancer service
**For Development**: Use **Option 3 (Local Binary)** for quick testing
**For CI/CD**: Use **Option 4 (Docker Container)** in pipelines

## 🔒 **Security Considerations**

```yaml
# Add network policies
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: k8sgpt-mcp-policy
spec:
  podSelector:
    matchLabels:
      app: k8sgpt-mcp
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: allowed-namespace
    ports:
    - protocol: TCP
      port: 8089
```

## 🚀 **Quick Start Commands**

```bash
# Fastest way to get MCP running (local)
k8sgpt auth add openai
k8sgpt serve --mcp --mcp-http --mcp-port 8089

# Fastest way to get MCP running (cluster)
helm install k8sgpt-mcp ./charts/k8sgpt \
  -f charts/k8sgpt/values-mcp-example.yaml \
  -n k8sgpt --create-namespace

kubectl port-forward -n k8sgpt svc/k8sgpt-mcp 8089:8089
```
