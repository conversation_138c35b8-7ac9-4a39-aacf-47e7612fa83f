deployment:
  image:
    repository: ghcr.io/k8sgpt-ai/k8sgpt
    tag: "" # defaults to Chart.appVersion if unspecified
  imagePullPolicy: Always
  annotations: {}
  env:
    model: "gpt-3.5-turbo"
    backend: "openai" # one of: [ openai | llama ]
  # MCP (Model Context Protocol) server configuration
  mcp:
    enabled: false # Enable MCP server
    port: "8089" # Port for MCP server
    http: true # Enable HTTP mode for MCP server
  resources:
    limits:
      cpu: "1"
      memory: "512Mi"
    requests:
      cpu: "0.2"
      memory: "156Mi"
  securityContext: {}
  # Set securityContext.runAsUser/runAsGroup if necessary. Values below were taken from https://github.com/k8sgpt-ai/k8sgpt/blob/main/container/Dockerfile
  # runAsUser: 65532
  # runAsGroup: 65532
secret:
  secretKey: "" # base64 encoded OpenAI token

service:
  type: ClusterIP
  annotations: {}

serviceMonitor:
  enabled: false
  additionalLabels: {}
