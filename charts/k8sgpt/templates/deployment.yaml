apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "k8sgpt.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  {{- if .Values.deployment.annotations }}
  annotations:
  {{- toYaml .Values.deployment.annotations | nindent 4 }}
  {{- end }}
  labels:
    {{- include "k8sgpt.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "k8sgpt.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "k8sgpt.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      {{- if .Values.deployment.securityContext }}
      securityContext:
        {{- toYaml .Values.deployment.securityContext | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ template "k8sgpt.fullname" . }}
      containers:
      - name: k8sgpt-container
        imagePullPolicy: {{ .Values.deployment.imagePullPolicy }}
        image: {{ .Values.deployment.image.repository }}:{{ .Values.deployment.image.tag | default .Chart.AppVersion }}
        ports:
        - containerPort: 8080
        {{- if .Values.deployment.mcp.enabled }}
        - containerPort: {{ .Values.deployment.mcp.port | int }}
        {{- end }}
        args: ["serve"
        {{- if .Values.deployment.mcp.enabled }}, "--mcp", "-v","--mcp-http", "--mcp-port", {{ .Values.deployment.mcp.port | quote }}
        {{- end }}
        ]
        {{- if .Values.deployment.resources }}
        resources:
        {{- toYaml .Values.deployment.resources | nindent 10 }}
        {{- end }}
        env:
        - name: K8SGPT_MODEL
          value: {{ .Values.deployment.env.model }}
        - name: K8SGPT_BACKEND
          value: {{ .Values.deployment.env.backend }}
        {{- if .Values.secret.secretKey }}
        - name: K8SGPT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-backend-secret
              key: secret-key
        {{- end }}
        - name: XDG_CONFIG_HOME
          value: /k8sgpt-config/
        - name: XDG_CACHE_HOME
          value: /k8sgpt-config/
        volumeMounts:
        - mountPath: /k8sgpt-config
          name: config
      volumes:
      - emptyDir: {}
        name: config
