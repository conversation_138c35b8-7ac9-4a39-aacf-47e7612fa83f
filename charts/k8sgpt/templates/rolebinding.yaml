apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "k8sgpt.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    {{- include "k8sgpt.labels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: {{ template "k8sgpt.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
roleRef:
  kind: ClusterRole
  name: {{ template "k8sgpt.fullname" . }}
  apiGroup: rbac.authorization.k8s.io