{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "k8sgpt.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels:
    {{- include "k8sgpt.labels" . | nindent 4 }}
  {{- if .Values.serviceMonitor.additionalLabels }}
    {{- toYaml .Values.serviceMonitor.additionalLabels | nindent 4 }}
  {{- end }}
spec:
  endpoints:
  - honorLabels: true
    path: /metrics
    port: metrics
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "k8sgpt.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}