name: Semantic PR Validation
on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize
defaults:
  run:
    shell: bash
jobs:
  validate:
    runs-on: ubuntu-latest
    permissions:
      contents: read # Needed for checking out the repository
      pull-requests: read # Needed for reading prs
    steps:
      - name: Validate Pull Request
        uses: amannn/action-semantic-pull-request@fdd4d3ddf614fbcd8c29e4b106d3bbe0cb2c605d # v6.0.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # Configure which types are allowed.
          # Default: https://github.com/commitizen/conventional-commit-types
          types: |
            feat
            fix
            build
            chore
            ci
            docs
            perf
            refactor
            revert
            style
            test
            deps
          scopes: |
            deps
          # Configure that a scope must always be provided.
          requireScope: false
          # When using "Squash and merge" on a PR with only one commit, GitHub
          # will suggest using that commit message instead of the PR title for the
          # merge commit, and it's easy to commit this by mistake. Enable this option
          # to also validate the commit message for one commit PRs.
          validateSingleCommit: true
          # Configure additional validation for the subject based on a regex.
          # This ensures the subject doesn't start with an uppercase character.
          subjectPattern: ^(?![A-Z]).+$
          # If `subjectPattern` is configured, you can use this property to override
          # the default error message that is shown when the pattern doesn't match.
          # The variables `subject` and `title` can be used within the message.
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            doesn't start with an uppercase character.