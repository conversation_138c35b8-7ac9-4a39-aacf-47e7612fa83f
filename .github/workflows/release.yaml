name: release

on:
  push:
    branches:
      - main
      - '[0-9]+.[0-9]+.x'
  workflow_dispatch:

defaults:
  run:
    shell: bash

jobs:
  release-please:
    permissions:
      contents: write  # for google-github-actions/release-please-action to create release commit
      pull-requests: write  # for google-github-actions/release-please-action to create release PR
    runs-on: ubuntu-latest
    outputs:
      releases_created: ${{ steps.release.outputs.releases_created }}
      tag_name: ${{ steps.release.outputs.tag_name }}
    # Release-please creates a PR that tracks all changes
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
     
      - uses: google-github-actions/release-please-action@e4dc86ba9405554aeba3c6bb2d169500e7d3b4ee # v4.1.1
        id: release
        with:
          command: manifest
          token: ${{secrets.GITHUB_TOKEN}}
          default-branch: main

  goreleaser:
    if: needs.release-please.outputs.releases_created == 'true'
    permissions:
      contents: write
    needs:
      - release-please
    runs-on: ubuntu-latest
    steps:
      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: false
          # all of these default to true, but feel free to set to
          # "false" if necessary for your workflow
          android: false
          dotnet: false
          haskell: false
          large-packages: true
          docker-images: true
          swap-storage: true
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 0
      - name: Set up Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5
        with:
          go-version: '1.22'
      - name: Download Syft
        uses: anchore/sbom-action/download-syft@55dc4ee22412511ee8c3142cbea40418e6cec693 # v0.17.8
      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@e435ccd777264be153ace6237001ef4d979d3a7a # v6
        with:
          # either 'goreleaser' (default) or 'goreleaser-pro'
          distribution: goreleaser
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.K8SGPT_BOT_SECRET }}
          SLACK_TOKEN: ${{ secrets.SLACK_TOKEN }}
#      - name: Update new version in krew-index
#        uses: rajatjindal/krew-release-bot@3d9faef30a82761d610544f62afddca00993eef9 # v0.0.47

  build-container:
    if: needs.release-please.outputs.releases_created == 'true'
    needs:
      - release-please
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
      id-token: write
    env:
      IMAGE_TAG: ghcr.io/k8sgpt-ai/k8sgpt:${{ needs.release-please.outputs.tag_name }}
      IMAGE_NAME: k8sgpt
    steps:
      - name: Checkout
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          submodules: recursive

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@184bdaa0721073962dff0199f1fb9940f07167d1 # v3
        with:
          registry: "ghcr.io"
          username: ${{ github.actor }}
          password: ${{ secrets.K8SGPT_BOT_SECRET }}

      - name: Build Docker Image
        uses: docker/build-push-action@263435318d21b8e681c14492fe198d362a7d2c83 # v6
        with:
          context: .
          file: ./container/Dockerfile
          platforms: linux/amd64,linux/arm64
          target: production
          tags: |
            ${{ env.IMAGE_TAG }}
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          cache-from: type=gha,scope=${{ github.ref_name }}-${{ env.IMAGE_TAG }}
          cache-to: type=gha,scope=${{ github.ref_name }}-${{ env.IMAGE_TAG }}

      - name: Generate SBOM
        uses: anchore/sbom-action@55dc4ee22412511ee8c3142cbea40418e6cec693 # v0.17.8
        with:
          image: ${{ env.IMAGE_TAG }}
          artifact-name: sbom-${{ env.IMAGE_NAME }}
          output-file: ./sbom-${{ env.IMAGE_NAME }}.spdx.json

      - name: Attach SBOM to release
        uses: softprops/action-gh-release@72f2c25fcb47643c292f7107632f7a47c1df5cd8 # v2
        with:
          tag_name: ${{ needs.release-please.outputs.tag_name }}
          files: ./sbom-${{ env.IMAGE_NAME }}.spdx.json
