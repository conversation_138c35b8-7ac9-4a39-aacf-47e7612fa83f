package config

import (
	"context"
	"fmt"

	schemav1 "buf.build/gen/go/k8sgpt-ai/k8sgpt/protocolbuffers/go/schema/v1"
	"github.com/k8sgpt-ai/k8sgpt/pkg/integration"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// syncIntegration is aware of the following events
// A new integration added
// An integration removed from the Integration block
func (h *<PERSON><PERSON>) syncIntegration(ctx context.Context,
	i *schemav1.AddConfigRequest) (*schemav1.AddConfigResponse, error,
) {

	fmt.Println("WARNING: syncIntegration is deprecated.")

	response := &schemav1.AddConfigResponse{}
	integrationProvider := integration.NewIntegration()
	if i.Integrations == nil {
		// If there are locally activate integrations, disable them
		err := h.deactivateAllIntegrations(integrationProvider)
		if err != nil {
			return response, status.Error(codes.NotFound, "deactivation error")
		}
		return response, nil
	}

	// Warning: This code is an example of an integration modifying the active filter list
	// This integration is no longer part of K8sGPT due to compatibility issues

	//coreFilters, _, _ := analyzer.ListFilters()
	// Update filters
	//activeFilters := viper.GetStringSlice("active_filters")
	//if len(activeFilters) == 0 {
	//	activeFilters = coreFilters
	//}
	//var err error = status.Error(codes.OK, "")
	//if err != nil {
	//	fmt.Println(err)
	//}
	//deactivateFunc := func(integrationRef integration.IIntegration) error {
	//	namespace, err := integrationRef.GetNamespace()
	//	if err != nil {
	//		return err
	//	}
	//	err = integrationProvider.Deactivate(trivyName, namespace)
	//	if err != nil {
	//		return status.Error(codes.NotFound, "integration already deactivated")
	//	}
	//	return nil
	//}
	//integrationRef, err := integrationProvider.Get(trivyName)
	//if err != nil {
	//	return response, status.Error(codes.NotFound, "provider get failure")
	//}
	//if i.Integrations.Trivy != nil {
	//	switch i.Integrations.Trivy.Enabled {
	//	case true:
	//		if b, err := integrationProvider.IsActivate(trivyName); err != nil {
	//			return response, status.Error(codes.Internal, "integration activation error")
	//		} else {
	//			if !b {
	//				err := integrationProvider.Activate(trivyName, i.Integrations.Trivy.Namespace,
	//					activeFilters, i.Integrations.Trivy.SkipInstall)
	//				if err != nil {
	//					return nil, err
	//				}
	//			} else {
	//				return response, status.Error(codes.AlreadyExists, "integration already active")
	//			}
	//		}
	//	case false:
	//		err = deactivateFunc(integrationRef)
	//		if err != nil {
	//			return nil, err
	//		}
	//		// This break is included purely for static analysis to pass
	//	}
	//} else {
	//	// If Trivy has been removed, disable it
	//	err = deactivateFunc(integrationRef)
	//	if err != nil {
	//		return nil, err
	//	}
	//}

	return response, nil
}

func (h *Handler) ListIntegrations(ctx context.Context, req *schemav1.ListIntegrationsRequest) (*schemav1.ListIntegrationsResponse, error) {

	fmt.Println("WARNING: ListIntegrations is deprecated.")

	//integrationProvider := integration.NewIntegration()
	// Update the requester with the status of Trivy
	//trivy, err := integrationProvider.Get(trivyName)
	//active := trivy.IsActivate()
	//var skipInstall bool
	//var namespace string = ""
	//if active {
	//	namespace, err = trivy.GetNamespace()
	//	if err != nil {
	//		return nil, status.Error(codes.NotFound, "namespace not found")
	//	}
	//	if namespace == "" {
	//		skipInstall = true
	//	}
	//}
	//
	//if err != nil {
	//	return nil, status.Error(codes.NotFound, "trivy integration")
	//}
	resp := &schemav1.ListIntegrationsResponse{
		//Trivy: &schemav1.Trivy{
		//	Enabled:     active,
		//	Namespace:   namespace,
		//	SkipInstall: skipInstall,
		//},
	}

	return resp, nil
}

func (*Handler) deactivateAllIntegrations(integrationProvider *integration.Integration) error {

	fmt.Println("WARNING: deactivateIntegrations is deprecated.")
	integrations := integrationProvider.List()
	for _, i := range integrations {
		b, _ := integrationProvider.IsActivate(i)
		if b {
			in, err := integrationProvider.Get(i)
			if err != nil {
				return err
			}
			namespace, err := in.GetNamespace()
			if err != nil {
				return err
			}
			if err == nil {
				if namespace != "" {
					err := integrationProvider.Deactivate(i, namespace)
					if err != nil {
						return err
					}
				} else {
					fmt.Printf("Skipping deactivation of %s, not installed\n", i)
				}
			} else {
				return err
			}
		}
	}
	return nil
}
