{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base", "helpers:pinGitHubActionDigests", ":gitSignOff", "group:allNonMajor"], "addLabels": ["dependencies"], "postUpdateOptions": ["gomodTidy", "gomodMassage"], "automerge": true, "automergeType": "pr", "schedule": ["at any time"], "platformAutomerge": true, "packageRules": [{"matchPackageNames": ["azure-sdk-for-go"], "enabled": true, "groupName": "azure-group"}, {"matchPackageNames": ["prometheus"], "enabled": true, "groupName": "prometheus-group"}, {"matchPackageNames": ["k8s.io", "sigs.k8s.io"], "enabled": true, "groupName": "kubernetes-group"}, {"matchPackageNames": ["golang"], "enabled": true, "groupName": "golang-group"}, {"matchUpdateTypes": ["minor", "patch"], "matchCurrentVersion": "!/^0/", "automerge": true}, {"matchManagers": ["gomod"], "addLabels": ["go"]}, {"matchManagers": ["github-actions"], "addLabels": ["github_actions"]}, {"matchManagers": ["dockerfile"], "addLabels": ["docker"]}], "regexManagers": [{"fileMatch": ["(^|\\/)Makefile$", "(^|\\/)Dockerfile", "(^|\\/).*\\.ya?ml$", "(^|\\/).*\\.toml$", "(^|\\/).*\\.sh$"], "matchStrings": ["# renovate: datasource=(?<datasource>.+?) depName=(?<depName>.+?)\\s.*?_VERSION ?(\\??=|\\: ?) ?\\\"?(?<currentValue>.+?)?\\\"?\\s"]}]}